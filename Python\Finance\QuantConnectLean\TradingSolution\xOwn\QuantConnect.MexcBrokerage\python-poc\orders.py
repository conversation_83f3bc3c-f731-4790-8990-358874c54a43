import hashlib
import json
import time
import uuid
from datetime import datetime, timedelta, timezone
from curl_cffi import requests

def md5(value):
    return hashlib.md5(value.encode("utf-8")).hexdigest()


def mexc_crypto(key, obj):
    date_now = str(int((datetime.now(timezone.utc) + timedelta(hours=8)).timestamp() * 1000))
    g = md5(key + date_now)[7:]
    s = json.dumps(obj, separators=(",", ":"))
    sign = md5(date_now + s + g)
    return {"time": date_now, "sign": sign}


class MexcOrderEmulator:
    def __init__(self):
        self.key = "APP99c7420d5052a8bb598874895eefa05fe4e36ce7786cfb3cdd9917e786b9cb82"
        self.orders = {}

    def get_android_headers(self, signature=None):
        headers = {
            "Accept-Encoding": "gzip",
            "app-language": "zh-MY",
            "Authentication": self.key,
            "Authorization": self.key,
            "client": "Android",
            "client-version": "6.19.1",
            "Connection": "Keep-Alive",
            "Content-Type": "application/json; charset=utf-8",
            "device-brand": "UkVETUk=",
            "device-id": "ffffffff-8c26-e8c1-ffff-ffffefd7ee7a",
            "device-model": "UkVETUkgUmVkbWkgSzMwIFBybw==",
            "Host": "futures.365huo.xyz",
            "language": "zh-MY",
            "network": "V0lGSQ==",
            "platform": "android",
            "uid": "38774483",
            "service-provider": "46011",
            "timezone": "UTC+8",
            "timezone-login": "UTC+08:00",
            "type": "0",
            "user-agent": "Dalvik/2.1.0 (Linux; U; Android 12; Redmi K30 Pro Build/SKQ1.211006.001)",
            "uuid": "ffffffff-8c26-e8c1-ffff-ffffefd7ee7a",
            "version": "6.19.1",
        }

        if signature:
            headers["x-mxc-nonce"] = signature["time"]
            headers["x-mxc-sign"] = signature["sign"]

        return headers

    def create_order(self, symbol="SOL_USDT", price=100, vol=2, side=1, leverage=100):
        order_data = {
            "bboTypeNum": 0,
            "flashClose": False,
            "isFlashTrade": False,
            "marketCeiling": False,
            "nonce": str(uuid.uuid4()),
            "priceProtect": "0",
            "reduceOnly": False,
            "type": 1,
            "leverage": str(leverage),
            "openType": 2,
            "price": str(price),
            "side": side,
            "symbol": symbol,
            "vol": str(vol),
        }

        signature = mexc_crypto(self.key, order_data)
        headers = self.get_android_headers(signature)

        url = "https://futures.365huo.xyz/api/v1/private/order/create"

        print(f"[CREATE ORDER] Making POST request to {url}")
        print(f"Headers: {json.dumps(dict(list(headers.items())[:5]), indent=2)}")
        print(f"Request Body: {json.dumps(order_data, indent=2)}")

        try:
            response = requests.post(url, headers=headers, json=order_data, timeout=30)
            response_data = response.json()

            print(f"Response Status: {response.status_code}")
            print(f"Response: {json.dumps(response_data, indent=2)}")

            if response_data.get("success"):
                order_id = response_data["data"]["orderId"]
                self.orders[order_id] = {"orderId": order_id, "symbol": symbol, "price": price, "vol": vol, "side": side, "leverage": leverage, "state": 2, "createTime": int(time.time() * 1000)}

            return response_data

        except Exception as e:
            error_response = {"success": False, "error": str(e)}
            print(f"Error: {error_response}")
            return error_response

    def cancel_order(self, order_id):
        cancel_data = [order_id]
        signature = mexc_crypto(self.key, cancel_data)
        headers = self.get_android_headers(signature)

        url = "https://futures.365huo.xyz/api/v1/private/order/cancel"

        print(f"[CANCEL ORDER] Making POST request to {url}")
        print(f"Headers: {json.dumps(dict(list(headers.items())[:5]), indent=2)}")
        print(f"Request Body: {json.dumps(cancel_data, indent=2)}")

        try:
            response = requests.post(url, headers=headers, json=cancel_data, timeout=30)

            print(f"Response Status: {response.status_code}")
            print(f"Raw Response Content: {response.text[:500]}...")

            try:
                response_data = response.json()
                print(f"Parsed JSON Response: {json.dumps(response_data, indent=2)}")

                if response_data.get("success") and order_id in self.orders:
                    del self.orders[order_id]

                return response_data

            except json.JSONDecodeError as json_error:
                error_response = {"success": False, "error": f"JSON decode error: {str(json_error)}", "raw_content": response.text, "status_code": response.status_code}
                print(f"JSON Parse Error: {error_response}")
                return error_response

        except Exception as e:
            error_response = {"success": False, "error": str(e)}
            print(f"Request Error: {error_response}")
            return error_response

    def cancel_all_orders(self):
        cancel_data = {}
        signature = mexc_crypto(self.key, cancel_data)
        headers = self.get_android_headers(signature)
        headers["Host"] = "futures.365huo.xyz"

        url = "https://futures.365huo.xyz/api/v1/private/order/cancel_all"

        print(f"[CANCEL ALL ORDERS] Making POST request to {url}")
        print(f"Headers: {json.dumps(dict(list(headers.items())[:5]), indent=2)}")
        print(f"Request Body: {json.dumps(cancel_data, indent=2)}")

        try:
            response = requests.post(url, headers=headers, json=cancel_data, timeout=30)

            print(f"Response Status: {response.status_code}")
            print(f"Response Headers: {dict(response.headers)}")
            print(f"Raw Response Content: {response.text[:500]}...")

            try:
                response_data = response.json()
                print(f"Parsed JSON Response: {json.dumps(response_data, indent=2)}")

                if response_data.get("success"):
                    cancelled_count = len(self.orders)
                    self.orders.clear()
                    print(f"Cancelled {cancelled_count} orders locally")

                return response_data

            except json.JSONDecodeError as json_error:
                error_response = {"success": False, "error": f"JSON decode error: {str(json_error)}", "raw_content": response.text, "status_code": response.status_code}
                print(f"JSON Parse Error: {error_response}")
                return error_response

        except Exception as e:
            error_response = {"success": False, "error": str(e)}
            print(f"Request Error: {error_response}")
            return error_response

    def get_open_orders(self):
        headers = self.get_android_headers()
        headers["uid"] = "38774483"

        url = "https://futures.365huo.xyz/api/v1/private/order/list/open_orders?page_num=1&page_size=200"

        print(f"[GET OPEN ORDERS] Making GET request to {url}")
        print(f"Headers: {json.dumps(dict(list(headers.items())[:5]), indent=2)}")

        try:
            response = requests.get(url, headers=headers, timeout=30)

            print(f"Response Status: {response.status_code}")
            print(f"Raw Response Content: {response.text[:500]}...")

            try:
                response_data = response.json()
                print(f"Parsed JSON Response: {json.dumps(response_data, indent=2)}")
                return response_data

            except json.JSONDecodeError as json_error:
                error_response = {"success": False, "error": f"JSON decode error: {str(json_error)}", "raw_content": response.text, "status_code": response.status_code}
                print(f"JSON Parse Error: {error_response}")
                return error_response

        except Exception as e:
            error_response = {"success": False, "error": str(e)}
            print(f"Request Error: {error_response}")
            return error_response


def test_scenario_1():
    print("=" * 60)
    print("TESTING SCENARIO 1: Create Order -> Sleep 60s -> Cancel Order")
    print("=" * 60)

    emulator = MexcOrderEmulator()

    create_response = emulator.create_order(symbol="SOL_USDT", price=100, vol=2)
    if create_response.get("success") and "data" in create_response:
        order_id = create_response["data"]["orderId"]
    else:
        print("Failed to create order, exiting scenario")
        return

    emulator.cancel_order(order_id)

    emulator.get_open_orders()

    print("\nScenario 1 completed!")


def test_scenario_2():
    print("=" * 60)
    print("TESTING SCENARIO 2: Create Order -> Sleep 60s -> Cancel All Orders")
    print("=" * 60)

    emulator = MexcOrderEmulator()

    print("\n1. Creating order...")
    create_response = emulator.create_order(symbol="SOL_USDT", price=100, vol=2)
    if create_response.get("success") and "data" in create_response:
        order_id = create_response["data"]["orderId"]
    else:
        print("Failed to create order, exiting scenario")
        return

    emulator.get_open_orders()

    emulator.cancel_all_orders()

    emulator.get_open_orders()

    print("\nScenario 2 completed!")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "1":
            test_scenario_1()
        elif sys.argv[1] == "2":
            test_scenario_2()
        else:
            print("Usage: python orders.py [1|2]")
            print("1 - Test scenario 1 (create -> cancel specific)")
            print("2 - Test scenario 2 (create -> cancel all)")
    else:
        print("Available test scenarios:")
        print("python orders.py 1  - Create order, sleep 60s, cancel order")
        print("python orders.py 2  - Create order, sleep 60s, cancel all orders")
        print("\nQuick test (no sleep):")
        emulator = MexcOrderEmulator()
        print("\nDemonstrating MD5 signature generation:")
        test_data = {"symbol": "SOL_USDT", "price": "100", "vol": "2"}
        signature = mexc_crypto(emulator.key, test_data)
        print(f"Key: {emulator.key}")
        print(f"Data: {json.dumps(test_data)}")
        print(f"Generated signature: {signature}")
        print(f"MD5 hash example: {md5('test_string')}")
        print("\nCreating test order...")
        response = emulator.create_order()
        print("\nGetting open orders...")
        emulator.get_open_orders()
